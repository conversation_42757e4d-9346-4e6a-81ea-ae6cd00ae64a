package com.dazhong.transportation.vlms;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.compress.utils.Lists;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import com.alibaba.fastjson.JSONObject;
import com.dazhong.transportation.vlms.config.DingTalkConfig;
import com.dazhong.transportation.vlms.dto.RowDto;
import com.dazhong.transportation.vlms.dto.VehicleDisposalDetailDto;
import com.dazhong.transportation.vlms.dto.request.CreateDingTalkWorkFlowRequest;
import com.dazhong.transportation.vlms.dto.request.GetDingTalkWorkFlowRequest;
import com.dazhong.transportation.vlms.dto.response.GetDingTalkDetailResponse;
import com.dazhong.transportation.vlms.dto.response.GetDingTalkUserResponse;
import com.dazhong.transportation.vlms.dto.response.GetDingTalkWorkFlowResponse;
import com.dazhong.transportation.vlms.service.IDingTalkService;

@SpringBootTest(classes = {Application.class})
public class IDingTalkServiceTest {

    @Autowired
    IDingTalkService iDingTalkService;

    @Autowired
    DingTalkConfig dingTalkConfig;

    @Test
    public void tesCreateDingTalkWorkFlow() throws Exception {
        CreateDingTalkWorkFlowRequest request = new CreateDingTalkWorkFlowRequest();
        request.setApproves(Lists.newArrayList());
        request.setProcessCode("PROC-6EE75BA0-5166-4717-999F-CE48BF74FCF6");
        request.setOriginatorUserId("056814156426145363");
        Map<String, String> processComponentValues = new HashMap<>();
        processComponentValues.put("姓名", "LSC");
        processComponentValues.put("年龄", "30");
        processComponentValues.put("日期", "2024-11-19");
        processComponentValues.put("爱好", "[\"爱好1\",\"爱好2\"]");
        processComponentValues.put("备注", "第一个审批流");
        processComponentValues.put("图片", "[\"https://evcard.oss-cn-shanghai.aliyuncs.com/evwork/velite6/%E6%B2%AAAD1234520190329082507091.png\"]");
        request.setProcessComponentValues(processComponentValues);
        String instanceId = iDingTalkService.createDingTalkWorkFlow(request);
    }

    /**
     * 7rMkC-O-RPGy4qstTYMmRA04961736478588
     */
    @Test
    public void testGetDingTalkWorkFlow() throws Exception {
        GetDingTalkWorkFlowRequest request = new GetDingTalkWorkFlowRequest();
        request.setInstanceId("7rMkC-O-RPGy4qstTYMmRA04961736478588");
        GetDingTalkWorkFlowResponse response = iDingTalkService.getDingTalkWorkFlow(request);
        System.out.println(JSONObject.toJSONString(response));
    }

    @Test
    public void testGetDingTalkUserByMobilePhone() throws Exception {
        GetDingTalkUserResponse response = iDingTalkService.getDingTalkUserByMobilePhone("13774337367");
        System.out.println(JSONObject.toJSONString(response));
    }

    @Test
    public void testQueryFormInstance() throws Exception {
        GetDingTalkWorkFlowRequest queryFormInstanceRequest = new GetDingTalkWorkFlowRequest();
        queryFormInstanceRequest.setInstanceId("O_6tx6HPQY2n-5Z8lWVxcw04961739931522");
        GetDingTalkDetailResponse getDingTalkDetailResponse = iDingTalkService.getDingTalkDetailFlow(queryFormInstanceRequest);
        List<RowDto> rowDtoList = getDingTalkDetailResponse.getDetailData();
        List<VehicleDisposalDetailDto> vehicleDisposalDetailDtoList = new ArrayList<>();
        for (RowDto rowDto : rowDtoList) {
            VehicleDisposalDetailDto vehicleDisposalDetailDto = rowDto.convertToVehicleDisposalDetailDto();
            vehicleDisposalDetailDtoList.add(vehicleDisposalDetailDto);
        }
        System.out.println(JSONObject.toJSONString(vehicleDisposalDetailDtoList));
    }

    @Test
    public void getInstanceDownloadFile() throws Exception {
        String url = iDingTalkService.getInstanceDownloadFile("V4hTI7JVQYesyQxBin1Q4A04961741676933", "171032219180", "上传图片(1).jpg");
        System.out.println(url);
    }
}