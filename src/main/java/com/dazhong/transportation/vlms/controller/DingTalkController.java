package com.dazhong.transportation.vlms.controller;

import com.dazhong.transportation.vlms.config.LoginRequiredAnnotation;
import com.dazhong.transportation.vlms.config.TokenUserAnnotation;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.DealDingTalkWorkFlowResultRequest;
import com.dazhong.transportation.vlms.dto.request.GetDingTalkWorkFlowRequest;
import com.dazhong.transportation.vlms.dto.response.DingTalkDepartmentTreeResponse;
import com.dazhong.transportation.vlms.dto.response.GetDingTalkWorkFlowResponse;
import com.dazhong.transportation.vlms.dto.response.base.ComboResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.service.IDingTalkService;
import com.dazhong.transportation.vlms.service.IVehicleApplicationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Api(value = "钉钉审批流接口", tags = "审批流")
@RestController
@RequestMapping(value = "api/dingTalk")
public class DingTalkController {

    @Resource
    private IDingTalkService dingTalkService;

    @Autowired
    private IVehicleApplicationService iVehicleApplicationService;


    @ApiOperation(value = "查询钉钉审批流程", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "/getDingTalkWorkFlowDetail", method = RequestMethod.POST)
    public ResultResponse<GetDingTalkWorkFlowResponse> getDingTalkWorkFlowDetail(@RequestBody GetDingTalkWorkFlowRequest request) {
        GetDingTalkWorkFlowResponse response = dingTalkService.getDingTalkWorkFlow(request);
        return ResultResponse.success(response);
    }

    @ApiOperation(value = "查询钉钉组织架构树", httpMethod = "GET")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "/getDepartmentTree", method = RequestMethod.GET)
    public ResultResponse<DingTalkDepartmentTreeResponse> getDepartmentTree() {
        List<DingTalkDepartmentTreeResponse>  departmentTree = dingTalkService.queryDepartmentTree();
        return ResultResponse.success(departmentTree);
    }

    @ApiOperation(value = "查询登录人钉钉组织下拉列表", httpMethod = "GET")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "/getDepartmentCombo", method = RequestMethod.GET)
    public ResultResponse<ComboResponse<Long, String>> getDepartmentCombo(@TokenUserAnnotation TokenUserInfo tokenUserInfo) {
        ComboResponse<Long, String>  departmentTree = dingTalkService.queryDepartmentCombo(tokenUserInfo);
        return ResultResponse.success(departmentTree);
    }


    @ApiOperation(value = "手动处理钉钉回调结果", httpMethod = "POST")
    @LoginRequiredAnnotation(required = true)
    @RequestMapping(value = "/dealDingFlowResult", method = RequestMethod.POST)
    public ResultResponse dealDingFlowResult(@RequestBody DealDingTalkWorkFlowResultRequest request) {
        dingTalkService.dealDingFlowResult(request);
        return ResultResponse.success();
    }
}
