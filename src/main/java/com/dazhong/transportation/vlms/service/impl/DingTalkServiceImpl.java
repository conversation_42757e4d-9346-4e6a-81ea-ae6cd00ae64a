package com.dazhong.transportation.vlms.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.aliyun.dingtalkworkflow_1_0.models.*;
import com.aliyun.tea.TeaException;
import com.aliyun.teautil.models.RuntimeOptions;
import com.dazhong.transportation.vlms.config.DingTalkConfig;
import com.dazhong.transportation.vlms.config.Global;
import com.dazhong.transportation.vlms.constant.DingTalkConstant;
import com.dazhong.transportation.vlms.database.TableUserService;
import com.dazhong.transportation.vlms.dto.DingTalkWorkFlowApproveDto;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.request.CreateDingTalkWorkFlowRequest;
import com.dazhong.transportation.vlms.dto.request.DealDingTalkWorkFlowResultRequest;
import com.dazhong.transportation.vlms.dto.request.GetDingTalkWorkFlowRequest;
import com.dazhong.transportation.vlms.dto.request.TerminateDingTalkProcessInstanceRequest;
import com.dazhong.transportation.vlms.dto.response.DingTalkDepartmentTreeResponse;
import com.dazhong.transportation.vlms.dto.response.GetDingTalkDetailResponse;
import com.dazhong.transportation.vlms.dto.response.GetDingTalkUserResponse;
import com.dazhong.transportation.vlms.dto.response.GetDingTalkWorkFlowResponse;
import com.dazhong.transportation.vlms.dto.response.base.ComboResponse;
import com.dazhong.transportation.vlms.enums.DingApprovalOperationTypeEnum;
import com.dazhong.transportation.vlms.exception.ExceptionEnum;
import com.dazhong.transportation.vlms.exception.ServiceException;
import com.dazhong.transportation.vlms.service.IDingTalkService;
import com.dazhong.transportation.vlms.utils.CommonUtils;
import com.dazhong.transportation.vlms.utils.DateTimeUtils;
import com.dazhong.transportation.vlms.utils.DingTalkCommentUtils;
import com.dazhong.transportation.vlms.utils.ValidationUtils;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiDepartmentListRequest;
import com.dingtalk.api.request.OapiV2UserGetRequest;
import com.dingtalk.api.request.OapiV2UserGetbymobileRequest;
import com.dingtalk.api.response.OapiDepartmentListResponse;
import com.dingtalk.api.response.OapiV2UserGetResponse;
import com.dingtalk.api.response.OapiV2UserGetbymobileResponse;
import com.taobao.api.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.net.URI;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 钉钉审批流相关接口
 */
@Service
@Slf4j
public class DingTalkServiceImpl implements IDingTalkService {

    @Autowired
    private DingTalkConfig dingTalkConfig;

    @Autowired
    private IDingTalkService dingTalkService;

    @Autowired
    private TableUserService tableUserService;

    @Override
    public String createDingTalkWorkFlow(CreateDingTalkWorkFlowRequest request) throws ServiceException {
        //验证参数
        ValidationUtils.validate(request);

        log.info("提交订单审批参数 {}", JSONUtil.toJsonStr(request));
        try {
            String accessToken = DingTalkCommentUtils.getAccessToken(
                    dingTalkConfig.getDingTalkAppKey(), dingTalkConfig.getDingTalkAppSecret());

            GetDingTalkUserResponse userResponse = getDingTalkUserByDingId(request.getOriginatorUserId());



            StartProcessInstanceRequest startProcessInstanceRequest = DingTalkCommentUtils.buildStartProcessInstanceRequest(
                    request.getApproves(), request.getProcessCode(), request.getOriginatorUserId(), request.getProcessComponentValues()
                    , dingTalkConfig.getDingTalkAgentId(), request.getAttachmentInfo(), accessToken
                    , userResponse.getUnionId(),request.getTargetSelectUserIds(),request.getOriginatorDeptId());

            com.aliyun.dingtalkworkflow_1_0.Client client = DingTalkCommentUtils.createClient();
            com.aliyun.dingtalkworkflow_1_0.models.StartProcessInstanceHeaders startProcessInstanceHeaders
                    = new com.aliyun.dingtalkworkflow_1_0.models.StartProcessInstanceHeaders();
            startProcessInstanceHeaders.xAcsDingtalkAccessToken = accessToken;

            StartProcessInstanceResponse startProcessInstanceResponse = client.startProcessInstanceWithOptions(
                    startProcessInstanceRequest, startProcessInstanceHeaders,
                    new RuntimeOptions());
            return startProcessInstanceResponse.getBody().getInstanceId();
        } catch (TeaException err) {
            log.error("createDingTalkWorkFlow exception {}", err);
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                throw new ServiceException(err.message);
            }
            throw new ServiceException(ExceptionEnum.DING_TALK_FAIL);
        } catch (Exception e) {
            log.error("createDingTalkWorkFlow exception ", e);
            throw new ServiceException(ExceptionEnum.DING_TALK_FAIL);
        }
    }

    @Override
    public GetDingTalkWorkFlowResponse getDingTalkWorkFlow(GetDingTalkWorkFlowRequest request) throws ServiceException {
        //验证参数
        ValidationUtils.validate(request);
        GetDingTalkWorkFlowResponse result = new GetDingTalkWorkFlowResponse();
        try {
            com.aliyun.dingtalkworkflow_1_0.Client client = DingTalkCommentUtils.createClient();
            com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceHeaders getProcessInstanceHeaders = new com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceHeaders();

            getProcessInstanceHeaders.xAcsDingtalkAccessToken = DingTalkCommentUtils.getAccessToken(
                    dingTalkConfig.getDingTalkAppKey(), dingTalkConfig.getDingTalkAppSecret());

            com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceRequest getProcessInstanceRequest = new com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceRequest()
                    .setProcessInstanceId(request.getInstanceId());
            GetProcessInstanceResponse response = client.getProcessInstanceWithOptions(getProcessInstanceRequest, getProcessInstanceHeaders, new com.aliyun.teautil.models.RuntimeOptions());
            if (DingTalkConstant.DING_TALK_HTTP_STATUS == response.getStatusCode()) {
                result.setInstanceId(request.getInstanceId());
                if (response.getBody() != null && response.getBody().getResult() != null) {
                    GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResult bodyResult = response.getBody().getResult();
                    result.setWorkFlowResult(DingTalkConstant.dingTalkResultMap.get(bodyResult.getResult()));
                    result.setWorkFlowStatus(DingTalkConstant.dingTalkStatusMap.get(bodyResult.getStatus()));
                    if (!CollectionUtils.isEmpty(bodyResult.operationRecords)) {
                        List<DingTalkWorkFlowApproveDto> approveDtoList = new ArrayList<>();
                        for (GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResultOperationRecords operationRecord : bodyResult.operationRecords) {
                            if (DingApprovalOperationTypeEnum.PROCESS_CC.getCode().equals(operationRecord.getType())) {
                                continue;
                            }
                            DingTalkWorkFlowApproveDto approveDto = new DingTalkWorkFlowApproveDto();
                            approveDto.setShowName(operationRecord.getShowName());
                            if(DingApprovalOperationTypeEnum.TERMINATE_PROCESS_INSTANCE.getCode().equals(operationRecord.getType())){
                                approveDto.setShowName(DingApprovalOperationTypeEnum.TERMINATE_PROCESS_INSTANCE.getDesc());
                            }
                            approveDto.setOptDateTime(DateTimeUtils.formatTargetPattern(operationRecord.getDate(), DateTimeUtils.DATE_TYPE5, DateTimeUtils.DATE_TYPE1));
                            if (!DingApprovalOperationTypeEnum.START_PROCESS_INSTANCE.getCode().equals(operationRecord.getType())
                                    && !DingApprovalOperationTypeEnum.TERMINATE_PROCESS_INSTANCE.getCode().equals(operationRecord.getType())) {
                                approveDto.setOptResult(DingTalkConstant.dingTalkOptResultMap.get(operationRecord.getResult()));
                            }
                            if (StringUtils.isNotBlank(operationRecord.getUserId())){
                                if(DingTalkConstant.FLOW_SYSTEM_USER.equals(operationRecord.getUserId())){
                                    approveDto.setOptUserName(DingTalkConstant.DEFAULT_SYSTEM_USER_TO_VIEW);
                                }else{
                                    try {
                                        GetDingTalkUserResponse userResponse = getDingTalkUserByDingId(operationRecord.getUserId());
                                        if (userResponse != null) {
                                            approveDto.setOptUserName(userResponse.getName());
                                        }else {
                                            approveDto.setOptUserName(operationRecord.getUserId());
                                        }
                                    }catch (Exception e){
                                        log.error("getDingTalkWorkFlow get user Exception ",e);
                                        approveDto.setOptUserName(operationRecord.getUserId());
                                    }
                                }
                            }
                            approveDto.setRemark(operationRecord.getRemark());
                            approveDto.setOptType(operationRecord.getType());
                            approveDtoList.add(approveDto);
                        }
                        result.setApproveDtoList(approveDtoList);
                    }
                }
            }
        } catch (TeaException err) {
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                throw new ServiceException(err.message);
            }
            throw new ServiceException(ExceptionEnum.DING_TALK_FAIL);
        } catch (Exception e) {
            log.error("getDingTalkWorkFlow exception ", e);
            throw new ServiceException(ExceptionEnum.DING_TALK_FAIL);
        }
        return result;
    }

    @Override
    public GetDingTalkUserResponse getDingTalkUserByMobilePhone(String mobilePhone) throws ServiceException {
        GetDingTalkUserResponse result = new GetDingTalkUserResponse();
        try {
            String accessToken = DingTalkCommentUtils.getAccessToken(
                    dingTalkConfig.getDingTalkAppKey(), dingTalkConfig.getDingTalkAppSecret());
            DingTalkClient clientByGetByMobile = new DefaultDingTalkClient(DingTalkConstant.USER_GET_BY_MOBILE_SERVER_URL);
            OapiV2UserGetbymobileRequest reqByMobile = new OapiV2UserGetbymobileRequest();
            reqByMobile.setMobile(mobilePhone);
            OapiV2UserGetbymobileResponse response = clientByGetByMobile.execute(reqByMobile, accessToken);
            if (!response.isSuccess()) {
                throw new ServiceException(response.getErrmsg());
            }

            if (StringUtils.isNotBlank(response.getResult().getUserid())) {
                DingTalkClient clientGetUser = new DefaultDingTalkClient(DingTalkConstant.GET_USER_INFO_SERVER_URL);
                OapiV2UserGetRequest reqByUser = new OapiV2UserGetRequest();
                reqByUser.setUserid(response.getResult().getUserid());
                reqByUser.setLanguage("zh_CN");
                OapiV2UserGetResponse rsp = clientGetUser.execute(reqByUser, accessToken);
                if (!rsp.isSuccess()) {
                    throw new ServiceException(rsp.getErrmsg());
                }
                if (rsp.isSuccess() && rsp.getResult() != null) {
                    result.setUserId(rsp.getResult().getUserid());
                    result.setName(rsp.getResult().getName());
                }
            }
        } catch (Exception e) {
            log.error("getDingTalkUserByMobilePhone exception ", e);
            throw new ServiceException(e.getMessage());
        }
        return result;
    }

    @Override
    public List<DingTalkDepartmentTreeResponse> queryDepartmentTree() {
        try {
            String accessToken = DingTalkCommentUtils.getAccessToken(
                    dingTalkConfig.getDingTalkAppKey(), dingTalkConfig.getDingTalkAppSecret());
            List<OapiDepartmentListResponse.Department> departmentList = getDepartments(accessToken);
            if (CollectionUtils.isEmpty(departmentList)) {
                return new ArrayList<>();
            }
            List<DingTalkDepartmentTreeResponse> departmentTree = CommonUtils.getDepartmentTree(departmentList);
            return departmentTree;
        } catch (Exception e) {
            log.error("getDepartmentList exception ", e);
            return new ArrayList<>();
        }
    }

    @Override
    public ComboResponse<Long, String> queryDepartmentCombo(TokenUserInfo tokenUserInfo) {
        ComboResponse<Long, String> result = new ComboResponse<>();
        try {
            String accessToken = DingTalkCommentUtils.getAccessToken(dingTalkConfig.getDingTalkAppKey(), dingTalkConfig.getDingTalkAppSecret());
            List<OapiDepartmentListResponse.Department> departmentList = getDepartments(accessToken);
            if (CollectionUtils.isEmpty(departmentList)) {
                result.setList(new ArrayList<>());
                return result;
            }

            // 根据钉钉UserId获取用户部门
            List<Long> deptIdList = dingTalkService.getDingTalkUserByDingId(tokenUserInfo.getDingTalkNum()).getDeptIdList();

            // 使用 Java 8 Stream API 进行匹配
            List<OapiDepartmentListResponse.Department> matchedDepartments = departmentList.stream()
                    .filter(department -> deptIdList.contains(department.getId()))
                    .collect(Collectors.toList());

            result.setList(matchedDepartments.stream().map(department -> new ComboResponse.ComboData<>(department.getId(), department.getName())).collect(Collectors.toList()));
        } catch (Exception e) {
            log.error("getDepartmentList exception ", e);
            result.setList(new ArrayList<>());
            return result;
        }

        return result;
    }

    @Override
    public void terminateProcessInstance(TerminateDingTalkProcessInstanceRequest request) throws ServiceException {
        try {
            com.aliyun.dingtalkworkflow_1_0.Client client = DingTalkCommentUtils.createClient();
            com.aliyun.dingtalkworkflow_1_0.models.TerminateProcessInstanceHeaders terminateProcessInstanceHeaders = new com.aliyun.dingtalkworkflow_1_0.models.TerminateProcessInstanceHeaders();
            terminateProcessInstanceHeaders.xAcsDingtalkAccessToken = DingTalkCommentUtils.getAccessToken(
                    dingTalkConfig.getDingTalkAppKey(), dingTalkConfig.getDingTalkAppSecret());
            com.aliyun.dingtalkworkflow_1_0.models.TerminateProcessInstanceRequest terminateProcessInstanceRequest = new com.aliyun.dingtalkworkflow_1_0.models.TerminateProcessInstanceRequest()
                    .setProcessInstanceId(request.getProcessInstanceId())
                    .setIsSystem(request.isSystem())
                    .setRemark(request.getRemark())
                    .setOperatingUserId(request.getOperatingUserId());
            client.terminateProcessInstanceWithOptions(terminateProcessInstanceRequest, terminateProcessInstanceHeaders, new com.aliyun.teautil.models.RuntimeOptions());

        } catch (TeaException err) {
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                throw new ServiceException(err.message);
            }
        } catch (Exception e) {
            log.error("terminateProcessInstance exception ", e);
            throw new ServiceException(ExceptionEnum.DING_TALK_FAIL);
        }
    }

    @Override
    public GetDingTalkUserResponse getDingTalkUserByDingId(String userId) throws ServiceException {
        GetDingTalkUserResponse result = new GetDingTalkUserResponse();
        try {
            DingTalkClient clientGetUser = new DefaultDingTalkClient(DingTalkConstant.GET_USER_INFO_SERVER_URL);
            OapiV2UserGetRequest reqByUser = new OapiV2UserGetRequest();
            reqByUser.setUserid(userId);
            reqByUser.setLanguage("zh_CN");
            OapiV2UserGetResponse rsp = clientGetUser.execute(reqByUser, DingTalkCommentUtils.getAccessToken(
                    dingTalkConfig.getDingTalkAppKey(), dingTalkConfig.getDingTalkAppSecret()));
            if (!rsp.isSuccess()) {
                throw new ServiceException(rsp.getErrmsg());
            }
            if (rsp.isSuccess() && rsp.getResult() != null) {
                result.setUserId(rsp.getResult().getUserid());
                result.setName(rsp.getResult().getName());
                result.setDeptIdList(rsp.getResult().getDeptIdList());
                result.setUnionId(rsp.getResult().getUnionid());
            }
            return result;
        } catch (TeaException err) {
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                throw new ServiceException(err.message);
            } else {
                throw new ServiceException(ExceptionEnum.DING_TALK_FAIL);
            }
        } catch (Exception e) {
            log.error("getDingTalkUserByDingId exception ", e);
            throw new ServiceException(ExceptionEnum.DING_TALK_FAIL);
        }
    }

    @Override
    public GetDingTalkDetailResponse getDingTalkDetailFlow(GetDingTalkWorkFlowRequest request) throws ServiceException {
        //验证参数
        ValidationUtils.validate(request);
        GetDingTalkDetailResponse result = new GetDingTalkDetailResponse();
        try {
            com.aliyun.dingtalkworkflow_1_0.Client client = DingTalkCommentUtils.createClient();
            com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceHeaders getProcessInstanceHeaders = new com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceHeaders();

            getProcessInstanceHeaders.xAcsDingtalkAccessToken = DingTalkCommentUtils.getAccessToken(
                    dingTalkConfig.getDingTalkAppKey(), dingTalkConfig.getDingTalkAppSecret());

            com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceRequest getProcessInstanceRequest = new com.aliyun.dingtalkworkflow_1_0.models.GetProcessInstanceRequest()
                    .setProcessInstanceId(request.getInstanceId());
            GetProcessInstanceResponse response = client.getProcessInstanceWithOptions(getProcessInstanceRequest, getProcessInstanceHeaders, new com.aliyun.teautil.models.RuntimeOptions());
            // TODO 临时添加日志
            log.info("getDingTalkWorkFlow response {}", JSONUtil.toJsonStr(response));
            if (DingTalkConstant.DING_TALK_HTTP_STATUS == response.getStatusCode()) {
                if (response.getBody() != null && response.getBody().getResult() != null) {
                    GetProcessInstanceResponseBody.GetProcessInstanceResponseBodyResult bodyResult = response.getBody().getResult();
                    result.setFormComponentValues(bodyResult.getFormComponentValues());
                    result.setWorkFlowResult(bodyResult.getResult());
                    return result;
                }
            }
        } catch (TeaException err) {
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                throw new ServiceException(err.message);
            }
            throw new ServiceException(ExceptionEnum.DING_TALK_FAIL);
        } catch (Exception e) {
            log.error("getDingTalkWorkFlow exception ", e);
            throw new ServiceException(ExceptionEnum.DING_TALK_FAIL);
        }
        return null;
    }

    @Override
    public String getInstanceDownloadFile(String processInstanceId, String fileId, String fileName) throws ServiceException {
        try {
            com.aliyun.dingtalkworkflow_1_0.Client client = DingTalkCommentUtils.createClient();
            com.aliyun.dingtalkworkflow_1_0.models.GrantProcessInstanceForDownloadFileHeaders grantProcessInstanceForDownloadFileHeaders = new com.aliyun.dingtalkworkflow_1_0.models.GrantProcessInstanceForDownloadFileHeaders();
            grantProcessInstanceForDownloadFileHeaders.xAcsDingtalkAccessToken = DingTalkCommentUtils.getAccessToken(
                    dingTalkConfig.getDingTalkAppKey(), dingTalkConfig.getDingTalkAppSecret());
            com.aliyun.dingtalkworkflow_1_0.models.GrantProcessInstanceForDownloadFileRequest grantProcessInstanceForDownloadFileRequest = new com.aliyun.dingtalkworkflow_1_0.models.GrantProcessInstanceForDownloadFileRequest()
                    .setProcessInstanceId(processInstanceId)
                    .setFileId(fileId);
            GrantProcessInstanceForDownloadFileResponse response = client.grantProcessInstanceForDownloadFileWithOptions(grantProcessInstanceForDownloadFileRequest, grantProcessInstanceForDownloadFileHeaders, new com.aliyun.teautil.models.RuntimeOptions());

            URI uri = new URI(response.getBody().getResult().getDownloadUri());

            String midPath = DateTimeFormatter.ofPattern("yyyyMMdd").format(LocalDateTime.now());
            String fileDirPath = Global.instance.mfsRootPath + "/" + midPath;
            File fileDir = new File(fileDirPath);
            if (!fileDir.exists()) {
                fileDir.mkdirs();
            }

            int lastIndex = fileName.lastIndexOf(".");
            String fileExt = "";
            if (lastIndex > 0) {
                fileExt = fileName.substring(lastIndex);
            }
            String newFileName = IdUtil.objectId() + fileExt;
            // 构建文件存储的完整路径
            Path filePath = Paths.get(fileDirPath, newFileName);
            // 将文件保存到指定路径
            Files.copy(uri.toURL().openStream(), filePath);

            return filePath.toString().replace("\\", "/");
        } catch (TeaException err) {
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                throw new ServiceException(err.message);
            } else {
                throw new ServiceException(ExceptionEnum.DING_TALK_FAIL);
            }
        } catch (Exception e) {
            log.error("getInstanceDownloadFile exception ", e);
            throw new ServiceException(ExceptionEnum.DING_TALK_FAIL);
        }
    }

    @Override
    public void dealDingFlowResult(DealDingTalkWorkFlowResultRequest request) {
        //车辆调拨、车辆转籍、车辆切换业务线
        if (Global.instance.dingTalkConfig.getVehicleAllocateProcessCode().equals(request.getProcessCode())
                || Global.instance.dingTalkConfig.getVehicleModifyBusinessProcessCode().equals(request.getProcessCode())
                || Global.instance.dingTalkConfig.getVehicleTransferProcessCode().equals(request.getProcessCode())) {
            Global.instance.vehicleApplicationService.dingTalkResultProcess(request.getProcessInstanceId(), request.getResult());
        }
        //车辆转固钉钉审批流CODE
        else if (Global.instance.dingTalkConfig.getVehicleTransferFixedProcessCode().equals(request.getProcessCode())) {

            Global.instance.transferFixedService.dingTalkResultProcess(request.getProcessInstanceId(), request.getResult());
        }
        //车辆采购钉钉审批流CODE
        else if (Global.instance.dingTalkConfig.getVehiclePurchaseProcessCode().equals(request.getProcessCode())) {
            Global.instance.vehiclePurchaseService.dingTalkResultProcess(request.getProcessInstanceId(), request.getResult());
        }
        //车辆处置钉钉审批流CODE
        else if (Global.instance.dingTalkConfig.getVehicleDisposalProcessCode().equals(request.getProcessCode()) ||
                Global.instance.dingTalkConfig.getVehicleScrapProcessCode().equals(request.getProcessCode()) ||
                Global.instance.dingTalkConfig.getVehicleBusinessSellProcessCode().equals(request.getProcessCode())) {
            Global.instance.vehicleDisposalService.dingTalkResultProcess(request.getProcessInstanceId(), request.getResult());
        }
        //车辆逆处置钉钉审批流CODE
        else if (Global.instance.dingTalkConfig.getVehicleReverseDisposalProcessCode().equals(request.getProcessCode())) {
            Global.instance.vehicleReverseDisposalService.dingTalkResultProcess(request.getProcessInstanceId(), request.getResult());
        }
    }

    private static List<OapiDepartmentListResponse.Department> getDepartments(String accessToken) throws ApiException {
        DingTalkClient client = new DefaultDingTalkClient(DingTalkConstant.GET_DEPARTMENT_SERVER_URL);
        OapiDepartmentListRequest req = new OapiDepartmentListRequest();
        req.setId("");// 不指定ID查询全部
        req.setFetchChild(true); // 不递归获取子部门
        OapiDepartmentListResponse rsp = client.execute(req, accessToken);
        if (rsp.getErrcode() != 0) {
            throw new ServiceException(rsp.getErrmsg());
        }
        List<OapiDepartmentListResponse.Department> department = rsp.getDepartment();
        return department;
    }
}
