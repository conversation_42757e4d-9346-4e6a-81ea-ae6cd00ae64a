package com.dazhong.transportation.vlms.service;

import java.util.List;

import com.dazhong.transportation.vlms.dto.DisposalDingTalkDetailDto;
import com.dazhong.transportation.vlms.dto.TokenUserInfo;
import com.dazhong.transportation.vlms.dto.VehicleDisposalListDto;
import com.dazhong.transportation.vlms.dto.request.ImportDisposalVehicleInfoRequest;
import com.dazhong.transportation.vlms.dto.request.SaveDisposalApplicationRequest;
import com.dazhong.transportation.vlms.dto.request.SearchVehicleDisposalListRequest;
import com.dazhong.transportation.vlms.dto.request.base.BaseImportFileUrlRequest;
import com.dazhong.transportation.vlms.dto.response.DisposalApplicationDetailResponse;
import com.dazhong.transportation.vlms.dto.response.UploadFileResponse;
import com.dazhong.transportation.vlms.dto.response.base.PageResponse;
import com.dazhong.transportation.vlms.dto.response.base.ResultResponse;
import com.dazhong.transportation.vlms.excel.ImportDisposalVehicleDetail;

public interface IVehicleDisposalService {

    /**
     * 查询车辆处置列表
     *
     * @param searchVehicleDisposalListRequest 查询车辆处置列表入参
     * @param tokenUserInfo                    用户登录信息
     * @return 返回车辆处置列表
     */
    PageResponse<VehicleDisposalListDto> queryVehicleDisposalPageResponse(SearchVehicleDisposalListRequest searchVehicleDisposalListRequest, TokenUserInfo tokenUserInfo);

    /**
     * 关联退牌任务
     *
     * @param id            主键id
     * @param taskNumber    退牌任务编号
     * @param tokenUserInfo 用户登录信息
     * @param isSaveLog     是否记录日志
     * @return 无结果
     */
    ResultResponse<Void> relateReturnLicenseTask(Long id, String taskNumber, TokenUserInfo tokenUserInfo, boolean isSaveLog);

    /**
     * 保存出售申请单
     *
     * @param saveDisposalApplicationRequest 保存出售申请单入参
     * @param tokenUserInfo                  用户登录信息
     * @return 主键id
     */
    ResultResponse<Long> saveApplication(SaveDisposalApplicationRequest saveDisposalApplicationRequest, TokenUserInfo tokenUserInfo);

    /**
     * 提交出售申请单
     *
     * @param saveDisposalApplicationRequest 创建退牌任务请求
     * @param tokenUserInfo                  用户登录信息
     * @return 返回符合条件的车牌任务列表
     */
    ResultResponse<Long> submitApplication(SaveDisposalApplicationRequest saveDisposalApplicationRequest, TokenUserInfo tokenUserInfo);

    /**
     * 作废出售申请单
     *
     * @param id            申请单id
     * @param tokenUserInfo 用户登录信息
     * @return 返回符合条件的车牌任务列表
     */
    ResultResponse<Void> cancelApplication(Long id, TokenUserInfo tokenUserInfo);

    /**
     * 撤回出售申请单
     *
     * @param id            申请单id
     * @param tokenUserInfo 用户登录信息
     * @return 返回符合条件的车牌任务列表
     */
    ResultResponse<Void> withdrawApplication(Long id, TokenUserInfo tokenUserInfo);

    /**
     * 查询详情
     *
     * @param id 主键id
     * @return 返回处置申请单详情
     */
    DisposalApplicationDetailResponse queryDisposalDetail(Long id);

    /**
     * 获取处置申请单钉钉回调明细
     *
     * @param id 主键id
     * @return 钉钉回调明细
     */
    List<DisposalDingTalkDetailDto> queryDisposalDingTalkDetailList(Long id);

    /**
     * 钉钉回调处理
     *
     * @param dingTalkNo     钉钉审批单号
     * @param dingTalkResult 钉钉审批回调结果
     */
    void dingTalkResultProcess(String dingTalkNo, String dingTalkResult);

    /**
     * 获取批量导入处置任务车辆明细
     *
     * @param request 导入文件信息入参
     * @return 返回符合条件的车牌号列表
     */
    List<ImportDisposalVehicleDetail> getDisposalVehicleDetailList(ImportDisposalVehicleInfoRequest request);

    /**
     * 保存二手车交易发票URL
     *
     * @param urlList 发票文件URL列表
     * @param disposalId 处置申请单id
     * @param tokenUserInfo 用户信息
     * @return 操作结果
     */
    ResultResponse<Void> saveSecondhandCarInvoiceUrls(List<UploadFileResponse> urlList, Long disposalId, TokenUserInfo tokenUserInfo);

    /**
     * 导入处置车辆信息
     *
     * @param request 导入处置车辆信息请求
     * @param tokenUserInfo 用户信息
     * @return 操作结果
     */
    ResultResponse<Void> importDisposalVehicleInfo(ImportDisposalVehicleInfoRequest request, TokenUserInfo tokenUserInfo);
}
