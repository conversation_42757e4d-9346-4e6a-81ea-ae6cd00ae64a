package com.dazhong.transportation.vlms.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-12-25 19:15
 */
@Data
@ApiModel(description = "用户信息")
public class TokenUserInfo implements Serializable {

    @ApiModelProperty(value = "用户ID")
    private Long userId;

    @ApiModelProperty(value = "用户名")
    private String name;

    @ApiModelProperty(value = "钉钉号")
    private String dingTalkNum;

    @ApiModelProperty(value = "用户关联组织架构，系统管理员默认为空")
    private List<Long> orgIdList;

    @ApiModelProperty(value = "用户关联资产所有公司，系统管理员默认为空")
    private List<Integer> ownerIdList;
}
